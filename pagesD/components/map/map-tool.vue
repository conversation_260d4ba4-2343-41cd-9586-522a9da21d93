<template>
  <view class="map-tool">
    <view
      class="item"
      @click="toolTap(item)"
      v-for="(item, index) in toolList"
      :key="item.id"
      v-if="(!item.isJamOnly || pageType === 'jam') && (isExpanded || index < 5)"
    >
      <image
        class="image"
        :src="item.checkd ? item.lightSrc : item.graySrc"
      ></image>
      <view class="text" :class="{'special-text-4': item.name.length === 4}">{{ item.name }}</view>
      <view class="line" v-if="!(isExpanded && index === toolList.length - 1) && !(!isExpanded && index === 4)"></view>
    </view>

    <!-- 折叠按钮 -->
    <view class="collapse-btn" @click="toggleExpand" v-if="visibleToolCount > 5">
      <uni-icons
        :type="isExpanded ? 'arrowup' : 'arrowdown'"
        size="20"
        color="#666666"
      ></uni-icons>
    </view>
  </view>
</template>

<script>
import uniIcons from '@/components/uni-icons/uni-icons.vue'

export default {
  components: {
    uniIcons
  },
  props: {
    pageType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isExpanded: false, // 是否展开
      toolList: [
        // {
        //   id: 1,
        //   type: "road",
        //   name: "优惠路段",
        //   checkd: true,
        //   graySrc:
        //     "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/road_gray.png",
        //   lightSrc:
        //     "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/road_light.png"
        // },
        // {
        //   id: 2,
        //   type: "jam",
        //   name: "拥堵",
        //   checkd: true,
        //   graySrc:
        //     "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/jam_gray.png",
        //   lightSrc:
        //     "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/jam_light.png"
        // },
        {
          id: 5,
          type: "charging",
          name: "充电服务",
          checkd: true,
          isJamOnly: true,
          graySrc:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/charg_gray.png",
          lightSrc:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/charg_light.png",
        },
        {
          id: 3,
          type: "work",
          name: "施工",
          checkd: true,
          graySrc:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/sg_gray.png",
          lightSrc:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/sg_light.png"
        },
        {
          id: 4,
          type: "event",
          name: "事件",
          checkd: true,
          graySrc:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/event_gray.png",
          lightSrc:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/new-home/event_light.png"
        },
        { //服务区
          id: 6,
          type: "highService",
          name: "服务区",
          checkd: false,
          graySrc:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/service_gray.png",
          lightSrc:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/service_light.png"
        },
        { //收费站
          id: 7,
          type: "tollStation",
          name: "收费站",
          checkd: false,
          graySrc:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/station_gray.png",
          lightSrc:
            "https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/station_light.png"
        },
        {
          id: 8,
          type: "trafficFlow",
          name: "车流预测",
          checkd: false,
          graySrc:
            "/static//agent/trafficFlow_gray.png",
          lightSrc:
            "/static//agent/trafficFlow_light.png"
        },
        {
          id: 9,
          type: "weatherWarning",
          name: "天气预警",
          checkd: false,
          graySrc:
            "/static//agent/weather_gray.png",
          lightSrc:
            "/static//agent/weather_light.png"
        }
      ]
    };
  },
  computed: {
    // 计算可见的工具数量（排除仅在jam页面显示的工具）
    visibleToolCount() {
      return this.toolList.filter(item => !item.isJamOnly || this.pageType === 'jam').length;
    }
  },
  methods: {
    toolTap(item) {
      if (item.type === 'charging') {
        uni.navigateTo({
          url: '/pagesD/charging/index'
        });
      } else {
        item.checkd = !item.checkd;
        this.$emit("toolTap", item, item.checkd);
      }
    },
    // 切换展开/折叠状态
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    }
  }
};
</script>

<style lang="scss" scoped>
.map-tool {
  width: 80rpx;
  box-sizing: border-box;
  padding: 20rpx 12rpx;
  padding-bottom: 0;
  background: #fff;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  .item {
    // width: 44rpx;
    margin-bottom: 14rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    &:last-child {
      margin-bottom: 0;
      .line {
        width: 0;
      }
    }
    .image {
      width: 44rpx;
      height: 44rpx;
      margin-bottom: 4rpx;
    }
    .text {
      width: 60rpx;
      white-space: break-spaces;
      padding-bottom: 12rpx;
      font-size: 20rpx;
      color: #666666;
      // border-bottom: 1rpx solid #e1e1e1;
      &.special-text-4 {
        width: 54rpx;
      }
    }
    .line {
      width: 56rpx;
      height: 1rpx;
      background: #e1e1e1;
    }
  }

  .collapse-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8rpx 0;
    margin-top: 8rpx;
    border-top: 1rpx solid #e1e1e1;
  }
}
</style>