<template>
  <view class="map-tool-floating">
    <!-- 主要工具按钮 -->
    <view class="main-tools">
      <view
        class="tool-item"
        @click="toolTap(item)"
        v-for="(item, index) in mainTools"
        :key="item.id"
      >
        <image
          class="tool-icon"
          :src="item.checkd ? item.lightSrc : item.graySrc"
        ></image>
        <view class="tool-text">{{ item.name }}</view>
      </view>
    </view>
    
    <!-- 更多工具按钮 -->
    <view class="more-btn" @click="toggleMore" v-if="hasMoreTools">
      <uni-icons 
        type="more" 
        size="24" 
        color="#666666"
      ></uni-icons>
    </view>
    
    <!-- 展开的工具面板 -->
    <view class="expanded-panel" v-if="showMore && hasMoreTools">
      <view class="panel-header">
        <text class="panel-title">更多工具</text>
        <view class="close-btn" @click="toggleMore">
          <uni-icons type="close" size="20" color="#999"></uni-icons>
        </view>
      </view>
      <view class="tools-grid">
        <view
          class="grid-item"
          @click="toolTap(item)"
          v-for="item in moreTools"
          :key="item.id"
        >
          <image
            class="grid-icon"
            :src="item.checkd ? item.lightSrc : item.graySrc"
          ></image>
          <view class="grid-text">{{ item.name }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import uniIcons from '@/components/uni-icons/uni-icons.vue'

export default {
  components: {
    uniIcons
  },
  props: {
    pageType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showMore: false,
      toolList: [
        // 这里放置您的工具列表数据
        // 为了演示，我只放几个示例
        {
          id: 1,
          name: '充电服务',
          type: 'charging',
          checkd: false,
          lightSrc: '/static/images/map/charging_light.png',
          graySrc: '/static/images/map/charging_gray.png'
        },
        {
          id: 2,
          name: '施工',
          type: 'construction',
          checkd: false,
          lightSrc: '/static/images/map/construction_light.png',
          graySrc: '/static/images/map/construction_gray.png'
        },
        // ... 其他工具
      ]
    };
  },
  computed: {
    visibleTools() {
      return this.toolList.filter(item => !item.isJamOnly || this.pageType === 'jam');
    },
    mainTools() {
      return this.visibleTools.slice(0, 3); // 显示前3个主要工具
    },
    moreTools() {
      return this.visibleTools.slice(3); // 其余工具
    },
    hasMoreTools() {
      return this.moreTools.length > 0;
    }
  },
  methods: {
    toolTap(item) {
      if (item.type === 'charging') {
        uni.navigateTo({
          url: '/pagesD/charging/index'
        });
      } else {
        item.checkd = !item.checkd;
        this.$emit("toolTap", item, item.checkd);
      }
    },
    toggleMore() {
      this.showMore = !this.showMore;
    }
  }
};
</script>

<style lang="scss" scoped>
.map-tool-floating {
  position: relative;
  
  .main-tools {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10rpx);
    border-radius: 20rpx;
    padding: 16rpx 12rpx;
    box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.1);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    
    .tool-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .tool-icon {
        width: 40rpx;
        height: 40rpx;
        margin-bottom: 6rpx;
      }
      
      .tool-text {
        font-size: 18rpx;
        color: #666;
        text-align: center;
        line-height: 1.2;
      }
    }
  }
  
  .more-btn {
    margin-top: 12rpx;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10rpx);
    border-radius: 16rpx;
    padding: 12rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
  }
  
  .expanded-panel {
    position: absolute;
    right: 100rpx;
    top: 0;
    width: 300rpx;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15rpx);
    border-radius: 20rpx;
    box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.15);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    z-index: 1000;
    
    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 24rpx 16rpx;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
      
      .panel-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }
      
      .close-btn {
        padding: 8rpx;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.05);
      }
    }
    
    .tools-grid {
      padding: 20rpx;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20rpx;
      
      .grid-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16rpx 8rpx;
        border-radius: 12rpx;
        transition: background-color 0.2s;
        
        &:active {
          background: rgba(0, 0, 0, 0.05);
        }
        
        .grid-icon {
          width: 36rpx;
          height: 36rpx;
          margin-bottom: 8rpx;
        }
        
        .grid-text {
          font-size: 20rpx;
          color: #666;
          text-align: center;
          line-height: 1.2;
        }
      }
    }
  }
}
</style>
