<template>
  <view class="map-tool-drawer">
    <!-- 收起状态的工具栏 -->
    <view class="collapsed-toolbar" v-if="!isExpanded">
      <view
        class="tool-item"
        @click="toolTap(item)"
        v-for="item in mainTools"
        :key="item.id"
      >
        <image
          class="tool-icon"
          :src="item.checkd ? item.lightSrc : item.graySrc"
        ></image>
      </view>
      <view class="expand-btn" @click="toggleExpand" v-if="hasMoreTools">
        <uni-icons type="arrowright" size="20" color="#666"></uni-icons>
      </view>
    </view>
    
    <!-- 展开状态的抽屉 -->
    <view class="expanded-drawer" v-if="isExpanded">
      <view class="drawer-header">
        <text class="drawer-title">地图工具</text>
        <view class="collapse-btn" @click="toggleExpand">
          <uni-icons type="arrowleft" size="20" color="#666"></uni-icons>
        </view>
      </view>
      
      <view class="tools-container">
        <view
          class="drawer-tool-item"
          @click="toolTap(item)"
          v-for="item in visibleTools"
          :key="item.id"
        >
          <image
            class="drawer-icon"
            :src="item.checkd ? item.lightSrc : item.graySrc"
          ></image>
          <text class="drawer-text">{{ item.name }}</text>
          <view class="status-indicator" v-if="item.checkd"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import uniIcons from '@/components/uni-icons/uni-icons.vue'

export default {
  components: {
    uniIcons
  },
  props: {
    pageType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isExpanded: false,
      toolList: [
        // 您的工具列表数据
      ]
    };
  },
  computed: {
    visibleTools() {
      return this.toolList.filter(item => !item.isJamOnly || this.pageType === 'jam');
    },
    mainTools() {
      return this.visibleTools.slice(0, 3);
    },
    hasMoreTools() {
      return this.visibleTools.length > 3;
    }
  },
  methods: {
    toolTap(item) {
      if (item.type === 'charging') {
        uni.navigateTo({
          url: '/pagesD/charging/index'
        });
      } else {
        item.checkd = !item.checkd;
        this.$emit("toolTap", item, item.checkd);
      }
    },
    toggleExpand() {
      this.isExpanded = !this.isExpanded;
    }
  }
};
</script>

<style lang="scss" scoped>
.map-tool-drawer {
  .collapsed-toolbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10rpx);
    border-radius: 20rpx;
    padding: 12rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    
    .tool-item {
      margin-bottom: 12rpx;
      display: flex;
      justify-content: center;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .tool-icon {
        width: 36rpx;
        height: 36rpx;
      }
    }
    
    .expand-btn {
      margin-top: 12rpx;
      padding: 8rpx;
      display: flex;
      justify-content: center;
      border-top: 1rpx solid rgba(0, 0, 0, 0.05);
    }
  }
  
  .expanded-drawer {
    width: 280rpx;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15rpx);
    border-radius: 20rpx;
    box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.15);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    
    .drawer-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 24rpx;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
      
      .drawer-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }
      
      .collapse-btn {
        padding: 8rpx;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.05);
      }
    }
    
    .tools-container {
      padding: 16rpx 0;
      max-height: 600rpx;
      overflow-y: auto;
      
      .drawer-tool-item {
        display: flex;
        align-items: center;
        padding: 16rpx 24rpx;
        position: relative;
        transition: background-color 0.2s;
        
        &:active {
          background: rgba(0, 0, 0, 0.05);
        }
        
        .drawer-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;
        }
        
        .drawer-text {
          flex: 1;
          font-size: 24rpx;
          color: #333;
        }
        
        .status-indicator {
          width: 12rpx;
          height: 12rpx;
          border-radius: 50%;
          background: #007AFF;
        }
      }
    }
  }
}
</style>
