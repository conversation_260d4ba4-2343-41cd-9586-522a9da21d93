<template>
  <view class="info-wrap polyline-info">
    <!-- 上滑提示栏 -->
    <view class="drag-bar" @touchstart="handleTouchStart" @touchmove.stop.prevent="handleDragBarMove"
      @touchend="handleTouchEnd"
      :style="{ height: expanded ? '66rpx' : '86rpx', background: expanded ? 'transparent' : '' }">
      <view class="line"></view>
      <view v-if="!expanded" class="text">向上滑动查看AI出行推荐</view>
    </view>

    <!-- 路线选择区域 - 仅在正常状态显示 -->
    <view class="polyline-box" v-if="!expanded">
      <view class="polyline-item" v-for="item in lineArr" :key="item.lineId" @click="selectLine(item)"
        :class="{ active: actvieId == item.lineId }">
        <view class="time">{{ item.duration }}</view>
        <view class="distance">
          <view class="cover-text">{{ item.distance }}公里 </view>
          <view class="cover-text" v-if="item.toll > 0">¥{{ item.toll }}</view>
        </view>
        <view class="tags">
          <view class="cover-text truck-tag" v-if="item.isTruck">
            <!-- <view class="truck-icon">🚚</view> -->
            <view>{{ item.truckType }}</view>
            <view class="plate-type">({{ item.plateType }})</view>
            <view class="truck-desc-inline" v-if="item.truckDesc">{{ item.truckDesc }}</view>
          </view>
          <view class="cover-text" v-if="!item.isTruck && item.tags.length <= 0">大众常选</view>
          <view class="cover-text" v-if="!item.isTruck && item.tags.length > 0">{{
            handleTags(item.tags)
            }}</view>
        </view>
        <view class="restriction" v-if="item.restriction">
          {{ item.restriction }}
        </view>
      </view>
    </view>

    <!-- 缩略状态显示最优路线 - 暂时屏蔽 -->
    <!-- <view class="minimized-info" v-if="minimized && lineArr.length > 0">
      <view class="best-route">
        <view class="time">{{ lineArr[actvieId] ? lineArr[actvieId].duration : (lineArr[0] ? lineArr[0].duration : '') }}</view>
        <view class="distance">{{ lineArr[actvieId] ? lineArr[actvieId].distance : (lineArr[0] ? lineArr[0].distance : '') }}公里</view>
        <view class="tags" v-if="lineArr[actvieId] || lineArr[0]">
          <view class="cover-text">{{ (lineArr[actvieId] || lineArr[0]).tags && (lineArr[actvieId] || lineArr[0]).tags.length > 0 ? handleTags((lineArr[actvieId] || lineArr[0]).tags) : '大众常选' }}</view>
        </view>
      </view>
    </view> -->

    <!-- 展开状态的滚动内容 -->
    <scroll-view v-if="expanded" scroll-y :style="{ height: 'calc(100vh - 390rpx)' }" class="expanded-content">
      <!-- 路线选择区域 - 在展开状态下显示在滚动区域内 -->
      <view class="polyline-box">
        <view class="polyline-item" v-for="item in lineArr" :key="item.lineId" @click="selectLine(item)"
          :class="{ active: actvieId == item.lineId }">
          <view class="time">{{ item.duration }}</view>
          <view class="distance">
            <view class="cover-text">{{ item.distance }}公里 </view>
            <view class="cover-text" v-if="item.toll > 0">¥{{ item.toll }}</view>
          </view>
          <view class="tags">
            <view class="cover-text truck-tag" v-if="item.isTruck">
              <!-- <view class="truck-icon">🚚</view> -->
              <view>{{ item.truckType }}</view>
              <view class="plate-type">({{ item.plateType }})</view>
              <view class="truck-desc-inline" v-if="item.truckDesc">{{ item.truckDesc }}</view>
            </view>
            <view class="cover-text" v-if="!item.isTruck && item.tags.length <= 0">大众常选</view>
            <view class="cover-text" v-if="!item.isTruck && item.tags.length > 0">{{
              handleTags(item.tags)
              }}</view>
          </view>
          <view class="restriction" v-if="item.restriction">
            {{ item.restriction }}
          </view>
        </view>
      </view>

      <!-- 路况信息 -->
      <view class="traffic-info">
        <view class="section-header">
          <view class="title">路况信息</view>
          <view class="fold-btn" @click="toggleSection('traffic')">
            <text class="fold-icon">{{ trafficFolded ? '展开' : '折叠' }}</text>
            <image
              class="arrow-icon"
              :src="trafficFolded
                ? 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-right.png'
                : 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-down.png'"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <view class="traffic-list" v-if="!trafficFolded">
          <view class="traffic-item" v-for="(traffic, index) in trafficInfos" :key="index">
            <view class="traffic-dot"></view>
            <view class="traffic-content">
              <!-- <view class="traffic-title">{{ traffic.title }}</view> -->
              <view class="traffic-desc">{{ traffic.description }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 服务区信息 -->
      <view class="service-area">
        <view class="section-header">
          <view class="title">途径{{ serviceAreas.length }}个服务区，{{ gasStations }}个可加油，{{ chargingStations }}个可充电</view>
          <view class="fold-btn" @click="toggleSection('service')">
            <text class="fold-icon">{{ serviceFolded ? '展开' : '折叠' }}</text>
            <image
              class="arrow-icon"
              :src="serviceFolded
                ? 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-right.png'
                : 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-down.png'"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <view class="service-list" v-if="!serviceFolded">
          <service-area-item v-for="(service, index) in serviceAreas" :key="index" :service="service"
            @facilityClick="handleFacilityClick" />
        </view>
      </view>

      <!-- 途径城市 -->
      <view class="cities-info">
        <view class="section-header">
          <view class="title">途径以下城市</view>
          <view class="fold-btn" @click="toggleSection('cities')">
            <text class="fold-icon">{{ citiesFolded ? '展开' : '折叠' }}</text>
            <image
              class="arrow-icon"
              :src="citiesFolded
                ? 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-right.png'
                : 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/h5/toc/arrow-down.png'"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <view class="cities-list" v-if="!citiesFolded">
          <route-city-item v-for="(city, index) in routeCities" :key="index" :city="city"
            @exploreClick="handleExploreClick" />
        </view>
      </view>

      <!-- AI智能出行入口 - 独立区域 -->
      <view class="ai-travel-section">
        <ai-smart-travel />
      </view>
    </scroll-view>
    <view class="bottom-btn" @click="openMapApp">
      开始导航
    </view>
  </view>
</template>

<script>
import { removeEnglish } from "../../common/utils";
import ServiceAreaItem from './service-area-item.vue';
import RouteCityItem from './route-city-item.vue';
import AiSmartTravel from '@/pages/home/<USER>/ai-smart-travel.vue';

export default {
  components: {
    ServiceAreaItem,
    RouteCityItem,
    AiSmartTravel
  },
  props: {
    lineArr: {
      type: Array,
      default: () => []
    },
    actvieId: {
      type: [Number, String],
      default: 0
    }
  },
  data() {
    return {
      expanded: false,
      minimized: false,
      touchStartY: 0,
      touchMoveY: 0,
      // 展开内容的折叠状态
      trafficFolded: false,
      serviceFolded: false,
      citiesFolded: false,
      // 模拟数据
      trafficInfos: [
        {
          title: 'G75兰海高速预测2025/05/30 8时20分至9时',
          description: 'G75兰海高速预测2025/05/30  8时20分至9时，南宁往北海方向南海北路段车流量上升，请注意行车安全。'
        },
        {
          title: 'G75兰海高速预测2025/05/30 8时20分至9时',
          description: 'G75兰海高速预测2025/05/30  8时20分至9时，南宁往北海方向 xx路段车流量上升，请注意行车安全。 xx路段车流量上升，请注意行车安全。'
        },
        {
          title: 'G75兰海高速预测2025/05/30 11时20分至14时',
          description: '南宁往北海方向 G75兰海高速预测2025/05/30 11时20分至14时，南宁往北海方向 xx路段车流量下降。'
        }
      ],
      serviceAreas: [
        {
          name: '金天服务区',
          distance: '2.2km',
          image: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png',
          toiletFlag: true,
          parkFlag: true,
          tankersFlag: true,
          chargingFlag: false,
          storeFlag: true,
          restaurantFlag: true
        },
        {
          name: '飒还服务区',
          distance: '2.2km',
          image: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png',
          toiletFlag: true,
          parkFlag: true,
          tankersFlag: true,
          chargingFlag: true,
          storeFlag: false,
          restaurantFlag: true
        },
        {
          name: '因天服务区',
          distance: '2.2km',
          image: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/home/<USER>/FWQ1.png',
          toiletFlag: true,
          parkFlag: true,
          tankersFlag: false,
          chargingFlag: true,
          storeFlag: true,
          restaurantFlag: false
        }
      ],
      routeCities: [
        {
          name: '钦州市',
          distance: '2.2km',
          temperature: '23°C',
          weather: '小雨',
          image: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/tollStation-1.png'
        },
        {
          name: '北海市',
          distance: '2.2km',
          temperature: '23°C',
          weather: '大雨',
          image: 'https://portal.gxetc.com.cn/public-static/img/miniprogram/static/weather/tollStation-1.png'
        }
      ]
    };
  },
  computed: {
    // 计算可加油的服务区数量
    gasStations() {
      return this.serviceAreas.filter(area => area.tankersFlag).length;
    },
    // 计算可充电的服务区数量
    chargingStations() {
      return this.serviceAreas.filter(area => area.chargingFlag).length;
    }
  },
  methods: {
    selectLine(row) {
      if (this.actvieId == row.lineId) return;
      this.$emit("selectLine", row.lineId);
    },
    handleTags(tags) {
      let arr = removeEnglish(tags);
      console.log(tags, arr);
      return arr.join("·");
    },
    openMapApp() {
      this.$emit("openMapApp");
    },
    handleTouchStart(e) {
      this.touchStartY = e.touches[0].pageY;
      this.touchMoveY = 0; // Reset moveY on new touch start
    },
    handleDragBarMove(e) {
      if (!this.touchStartY) return;

      this.touchMoveY = e.touches[0].pageY - this.touchStartY;

      // 向上滑动超过30px
      if (this.touchMoveY < -30) {
        if (!this.expanded) {
          // 如果是正常状态，则展开
          this.expanded = true;
        }
        this.touchStartY = 0; // Reset startY after state change to prevent immediate re-triggering
      }
      // 向下滑动超过30px
      else if (this.touchMoveY > 30) {
        if (this.expanded) {
          // 如果是展开状态，恢复到正常状态
          this.expanded = false;
        }
        this.touchStartY = 0; // Reset startY after state change
      }
    },
    handleTouchEnd() {
      // Reset touch start info on touch end
      this.touchStartY = 0;
      this.touchMoveY = 0;
    },

    // 切换各个区域的折叠状态
    toggleSection(section) {
      switch (section) {
        case 'traffic':
          this.trafficFolded = !this.trafficFolded;
          break;
        case 'service':
          this.serviceFolded = !this.serviceFolded;
          break;
        case 'cities':
          this.citiesFolded = !this.citiesFolded;
          break;
      }
    },
    // 处理设施点击事件
    handleFacilityClick({ service, facilityKey }) {
      console.log('设施点击:', service.name, facilityKey);
      // 可以在这里添加更多的处理逻辑
    },

    // 处理探索城市旅游点击事件
    handleExploreClick(city) {
      console.log('探索城市旅游:', city.name);
      // 可以在这里添加跳转到城市旅游页面的逻辑
      // uni.navigateTo({
      //   url: `/pages/city-tourism?cityName=${city.name}`
      // });
    }
  }
};
</script>

<style lang="scss" scoped>
.polyline-info {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  transition: height 0.3s ease;
  z-index: 9999;
}

.info-wrap {
  width: 100%;
  height: auto;
  // min-height: 200rpx;
  // max-height: 520rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 0rpx 0rpx;
  padding: 0 20rpx 20rpx 20rpx;
  box-sizing: border-box;
  transition: height 0.3s ease;

  .bottom-btn {
    width: 342rpx;
    height: 76rpx;
    background: #0066e9;
    border-radius: 52rpx 52rpx 52rpx 52rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #ffffff;
    text-align: center;
    line-height: 76rpx;
    margin: 0 auto;
    margin-top: 24rpx;
  }


  .polyline-box {
    display: flex;
    box-sizing: border-box;
    margin-bottom: 30rpx;
    border-radius: 0rpx 16rpx 0rpx 0rpx;

    .polyline-item {
      flex: 1;
      // background: rgba(234, 241, 246, 0.5);
      background-image: linear-gradient(180deg, #eaf1f6 0%, #ffffff 100%);
      padding: 24rpx 20rpx 0 20rpx;
      color: #333333;
      text-align: center;

      &.active {
        color: #4f90ff;
        background: #ffffff;
      }

      .time {
        font-size: 32rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        margin-bottom: 16rpx;
      }

      .distance {
        font-weight: 400;
        font-size: 24rpx;
        // margin-bottom: 16rpx;
      }

      .tags {
        font-size: 24rpx;

        .truck-tag {
          color: #0066e9;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;

          .truck-icon {
            margin-right: 4rpx;
          }

          .plate-type {
            display: inline;
            font-size: 20rpx;
            color: #0066e9;
            margin-left: 4rpx;
          }

          .truck-desc-inline {
            font-size: 20rpx;
            color: #666666;
            margin-left: 8rpx;
          }
        }
      }


      .restriction {
        font-size: 20rpx;
        color: #ff5454;
        margin-top: 6rpx;
        max-width: 100%;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }

    .cover-text {
      display: inline-block;
    }
  }

  .drag-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 16rpx;

    .line {
      width: 146rpx;
      height: 8rpx;
      background: #9B9B9B;
      border-radius: 3rpx;
      margin-bottom: 16rpx;
    }

    .text {
      font-size: 24rpx;
      color: #3282E8;
    }
  }

  // 缩略状态样式 - 暂时屏蔽
  /* .minimized-info {
    padding: 20rpx 0;

    .best-route {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .time {
        font-size: 32rpx;
        font-weight: 600;
        color: #4f90ff;
      }

      .distance {
        font-size: 24rpx;
        color: #333333;
      }

      .tags {
        font-size: 24rpx;
        color: #666666;
      }
    }
  } */

  // 展开状态详细内容样式
  .expanded-content {
    flex: 1;
    background: #ffffff;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333333;
      }

      .fold-btn {
        display: flex;
        align-items: center;

        .fold-icon {
          font-size: 24rpx;
          color: #0066e9;
        }

        .arrow-icon {
          width: 24rpx;
          height: 24rpx;
          margin-left: 8rpx;
        }
      }
    }
  }

  // 路况信息样式
  .traffic-info {
    margin-bottom: 40rpx;

    .traffic-list {
      background: #F6F6F6;
      padding: 20rpx;

      .traffic-item {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;

        .traffic-dot {
          width: 12rpx;
          height: 12rpx;
          border-radius: 50%;
          background: #ffa500;
          margin-right: 16rpx;
          flex-shrink: 0;
        }

        .traffic-content {
          flex: 1;

          .traffic-title {
            font-size: 26rpx;
            color: #333333;
            margin-bottom: 8rpx;
            font-weight: 500;
          }

          .traffic-desc {
            font-weight: 400;
            font-size: 26rpx;
            color: #666666;
          }
        }
      }
    }
  }

  // 服务区样式
  .service-area {
    margin-bottom: 30rpx;

    .service-list {
      background: #F6F6F6;
      padding: 0 20rpx 20rpx 20rpx;
    }
  }

  .cities-info{
    margin-bottom: 30rpx;
    .cities-list{
      background: #F6F6F6;
      padding: 0 20rpx 20rpx 20rpx;
    }
  }

  // AI智能出行区域样式
  .ai-travel-section {
    padding-bottom: 20rpx;
  }
}
</style>